/* ===== Responsive Design ===== */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
}

/* Large Devices (1200px and up) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
}

/* Medium Devices (992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .nav-list {
        gap: var(--spacing-lg);
    }
}

/* Small Devices (768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-md);
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    /* إعادة ترتيب العناصر في Hero */
    .hero-visual {
        order: -2; /* لوحة التحكم أولاً */
    }

    .hero-text {
        order: -1; /* النص ثانياً */
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-xl);
        margin: var(--spacing-xl) 0;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }

    .tab-buttons {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .tab-btn {
        flex: 1;
        min-width: 150px;
    }

    /* Contact Section */
    .contact .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
}

/* Extra Small Devices (576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-md);
    }

    .hero {
        min-height: 80vh;
        padding: var(--spacing-3xl) 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    /* إعادة ترتيب العناصر في Hero */
    .hero-visual {
        order: -2; /* لوحة التحكم أولاً */
    }

    .hero-text {
        order: -1; /* النص ثانياً */
    }

    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .hero-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-lg);
        margin: var(--spacing-xl) 0;
        flex-wrap: wrap;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .btn-lg {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .nav-brand .brand-name {
        font-size: var(--font-size-xl);
    }

    .features {
        padding: var(--spacing-2xl) 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .feature-card {
        padding: var(--spacing-xl);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-base);
    }

    .services {
        padding: var(--spacing-2xl) 0;
    }

    .tab-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .tab-btn {
        width: 100%;
        text-align: center;
    }

    .tab-content {
        padding: var(--spacing-xl);
    }

    /* Contact Section */
    .contact .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .contact .pricing-card {
        text-align: center;
    }

    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
        text-align: left;
    }

    .footer-logo {
        justify-content: flex-start;
    }

    .hero-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
        max-width: 100%;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 120px;
    }

    .floating-card .icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
}

/* Mobile Devices (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    /* Header Adjustments */
    .navbar {
        padding: var(--spacing-sm) 0;
    }

    .nav-brand {
        gap: var(--spacing-sm);
    }

    .logo {
        height: 32px;
    }

    .brand-name {
        font-size: var(--font-size-lg);
    }

    .nav-actions {
        gap: var(--spacing-sm);
    }

    .nav-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* Language Switcher */
    .language-switcher {
        top: var(--spacing-sm);
        left: var(--spacing-sm);
    }

    .lang-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    /* Hero Section - إعادة ترتيب العناصر */
    .hero {
        min-height: 70vh;
        padding: var(--spacing-2xl) 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
        /* إعادة ترتيب العناصر: لوحة التحكم أولاً */
    }

    .hero-visual {
        order: -2; /* لوحة التحكم تظهر أولاً */
        margin-bottom: var(--spacing-xl);
    }

    .hero-text {
        order: -1; /* النص يظهر ثانياً */
    }

    .hero-badge {
        margin-bottom: var(--spacing-md);
        font-size: var(--font-size-sm);
        padding: var(--spacing-xs) var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-lg);
        line-height: 1.5;
    }

    /* إحصائيات Hero - تحسين العرض */
    .hero-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-lg);
        margin: var(--spacing-xl) 0;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
        min-width: 80px;
    }

    .stat-number {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--primary);
    }

    .stat-plus {
        font-size: var(--font-size-lg);
        color: var(--primary);
    }

    .stat-label {
        font-size: var(--font-size-xs);
        color: var(--gray-600);
        margin-top: var(--spacing-xs);
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-xl);
    }

    .btn-lg {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    /* تحسين لوحة التحكم على الموبايل */
    .modern-dashboard {
        max-width: 100%;
        transform: scale(0.9);
    }

    .dashboard-main {
        padding: var(--spacing-lg);
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .metric-item {
        padding: var(--spacing-md);
        text-align: center;
    }

    .hero-graphic {
        height: 300px;
    }

    .hero-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        max-width: 280px;
        margin: 0 auto;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 140px;
        gap: var(--spacing-sm);
    }

    .floating-card .icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Sections */
    .features,
    .services {
        padding: var(--spacing-xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .section-subtitle {
        font-size: var(--font-size-sm);
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .feature-card h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
    }
    
    .feature-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Services */
    .tab-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .tab-content {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }
    
    /* Contact Section - تحسين قسم تواصل معنا */
    .contact .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .contact .pricing-card {
        padding: var(--spacing-lg);
        text-align: center;
    }

    .contact .plan-icon .icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
        margin: 0 auto var(--spacing-md);
    }

    .contact .plan-name {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
    }

    .contact .pricing-header p {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--primary);
        margin-bottom: var(--spacing-md);
    }

    .contact .btn {
        width: 100%;
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }

    /* Contact Form */
    .contact-form {
        padding: var(--spacing-xl);
        margin: 0 var(--spacing-sm);
    }

    .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-group label {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
        display: block;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
        border-radius: var(--radius-md);
    }

    /* Footer - تحسين الفوتر */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .footer-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    .footer-logo .logo {
        height: 40px;
        width: auto;
    }

    .footer-logo h3 {
        font-size: var(--font-size-xl);
        color: var(--white);
        margin: 0;
    }

    .footer-section p {
        font-size: var(--font-size-sm);
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }

    .footer-section h4 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
        color: var(--white);
    }

    .footer-links {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .footer-links li {
        margin-bottom: var(--spacing-sm);
    }

    .footer-links a {
        color: var(--gray-300);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: color 0.3s ease;
    }

    .footer-links a:hover {
        color: var(--primary);
    }

    .contact-info-footer {
        text-align: center;
    }

    .contact-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        font-size: var(--font-size-sm);
        color: var(--gray-300);
    }

    .contact-item i {
        color: var(--primary);
        width: 20px;
        text-align: center;
    }

    .whatsapp-link {
        color: var(--success) !important;
        text-decoration: none;
    }

    .social-links {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
    }

    .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: var(--primary);
        color: var(--white);
        border-radius: 50%;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .social-link:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
    }

    .footer-bottom {
        margin-top: var(--spacing-2xl);
        padding-top: var(--spacing-xl);
        border-top: 1px solid var(--gray-700);
        text-align: center;
    }

    .footer-bottom-content p {
        font-size: var(--font-size-xs);
        color: var(--gray-400);
        margin-bottom: var(--spacing-sm);
    }

    .footer-bottom-content a {
        color: var(--primary);
        text-decoration: none;
    }

    /* Scroll Indicator */
    .scroll-indicator {
        bottom: var(--spacing-md);
    }

    .scroll-arrow {
        height: 20px;
    }
}

/* تحسينات إضافية للموبايل */
@media (max-width: 767px) {
    /* ضمان ظهور لوحة التحكم أولاً على جميع الأجهزة المحمولة */
    .hero-visual {
        order: -2 !important;
        margin-bottom: var(--spacing-lg);
    }

    .hero-text {
        order: -1 !important;
    }

    /* تحسين عرض الإحصائيات */
    .hero-stats {
        order: 0;
        margin: var(--spacing-lg) 0 var(--spacing-xl);
    }

    /* تحسين عرض الأزرار */
    .hero-buttons {
        order: 1;
    }

    /* تحسين قسم تواصل معنا */
    .contact .pricing-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-md);
    }

    .contact .pricing-card {
        margin-bottom: var(--spacing-md);
    }

    /* تحسين الفوتر */
    .footer-content {
        grid-template-columns: 1fr !important;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .footer-logo {
        justify-content: center;
    }

    .contact-info-footer .contact-item {
        justify-content: center;
    }
}

/* Landscape Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        text-align: left;
    }

    /* الحفاظ على الترتيب حتى في الوضع الأفقي */
    .hero-visual {
        order: -1;
    }

    .hero-text {
        order: 0;
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-md);
    }

    .btn-lg {
        width: auto;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .language-switcher,
    .hero-buttons,
    .scroll-indicator {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-text {
        color: var(--gray-900) !important;
    }
    
    .section-title,
    .hero-title {
        color: var(--gray-900) !important;
    }
    
    .feature-card,
    .tab-content {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .floating-card,
    .hero-particles,
    .scroll-arrow {
        animation: none !important;
    }
    
    .animate-fade-up,
    .animate-fade-left,
    .animate-fade-right,
    .animate-on-scroll {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* شاشات صغيرة جداً (أقل من 400px) */
@media (max-width: 399px) {
    .container {
        padding: 0 var(--spacing-xs);
    }

    .hero-title {
        font-size: var(--font-size-lg) !important;
        line-height: 1.4;
    }

    .hero-subtitle {
        font-size: var(--font-size-xs) !important;
    }

    .hero-stats {
        gap: var(--spacing-sm);
    }

    .stat-item {
        min-width: 70px;
    }

    .stat-number {
        font-size: var(--font-size-lg) !important;
    }

    .stat-label {
        font-size: 10px !important;
    }

    .btn-lg {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .contact .pricing-card {
        padding: var(--spacing-md);
    }

    .contact .plan-name {
        font-size: var(--font-size-base);
    }

    .footer-logo h3 {
        font-size: var(--font-size-lg);
    }

    .contact-item {
        font-size: 12px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }

    .header {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-700);
    }

    .dropdown-menu {
        background: var(--gray-800);
        border: 1px solid var(--gray-700);
    }

    .lang-btn {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: var(--gray-200);
    }
}
