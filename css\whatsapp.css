/* ===== WhatsApp Floating Button ===== */
.whatsapp-float {
    position: fixed;
    bottom: 25px;
    right: 25px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(100px) scale(0.8);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.whatsapp-float.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.whatsapp-button {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
}

.whatsapp-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.4);
}

.whatsapp-button.clicked {
    transform: scale(0.95);
}

.whatsapp-icon {
    font-size: 30px;
    color: white;
    z-index: 2;
    transition: all 0.3s ease;
}

.whatsapp-button:hover .whatsapp-icon {
    transform: scale(1.1);
    animation: bounce 0.6s ease, shake 0.5s ease;
}

/* Simple Pulse Animation */
.whatsapp-pulse,
.whatsapp-pulse-2 {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 3px solid #25d366;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: simplePulse 2s infinite;
    opacity: 0;
}

.whatsapp-pulse-2 {
    animation-delay: 1s;
}

@keyframes simplePulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

/* Tooltip */
.whatsapp-tooltip {
    position: absolute;
    bottom: 75px;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.whatsapp-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 20px;
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

.whatsapp-float:hover .whatsapp-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Floating Animation */
.whatsapp-button {
    animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    }
    100% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6), 0 0 30px rgba(37, 211, 102, 0.3);
    }
}

.whatsapp-button:hover {
    animation: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-button {
        width: 65px;
        height: 65px;
    }

    .whatsapp-icon {
        font-size: 28px;
    }

    .whatsapp-tooltip {
        font-size: 13px;
        padding: 6px 10px;
        bottom: 75px;
    }
}

@media (max-width: 480px) {
    .whatsapp-float {
        bottom: 15px;
        right: 15px;
    }

    .whatsapp-button {
        width: 60px;
        height: 60px;
    }

    .whatsapp-icon {
        font-size: 26px;
    }

    .whatsapp-tooltip {
        font-size: 12px;
        padding: 5px 8px;
        bottom: 70px;
    }
}

/* ===== Call Button ===== */
.call-float {
    position: fixed;
    bottom: 100px;
    right: 25px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(100px) scale(0.8);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.call-float.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.call-button {
    position: relative;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
    animation: float 3s ease-in-out infinite;
    animation-delay: 1s;
}

.call-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.call-button.clicked {
    transform: scale(0.95);
}

.call-icon {
    font-size: 26px;
    color: white;
    z-index: 2;
    transition: all 0.3s ease;
}

.call-button:hover .call-icon {
    transform: scale(1.1);
    animation: bounce 0.6s ease;
}

.call-tooltip {
    position: absolute;
    bottom: 65px;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.call-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 15px;
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

.call-float:hover .call-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Responsive for Call Button */
@media (max-width: 768px) {
    .call-float {
        bottom: 95px;
        right: 20px;
    }

    .call-button {
        width: 55px;
        height: 55px;
    }

    .call-icon {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .call-float {
        bottom: 85px;
        right: 15px;
    }

    .call-button {
        width: 50px;
        height: 50px;
    }

    .call-icon {
        font-size: 22px;
    }
}

/* ===== RTL Support for Floating Buttons ===== */
[dir="ltr"] .whatsapp-float {
    right: auto;
    left: 25px;
}

[dir="ltr"] .call-float {
    right: auto;
    left: 25px;
}

[dir="ltr"] .whatsapp-tooltip {
    right: auto;
    left: 0;
}

[dir="ltr"] .whatsapp-tooltip::after {
    right: auto;
    left: 20px;
}

[dir="ltr"] .call-tooltip {
    right: auto;
    left: 0;
}

[dir="ltr"] .call-tooltip::after {
    right: auto;
    left: 15px;
}

/* RTL Responsive */
@media (max-width: 768px) {
    [dir="ltr"] .whatsapp-float {
        left: 20px;
    }

    [dir="ltr"] .call-float {
        left: 20px;
    }
}

@media (max-width: 480px) {
    [dir="ltr"] .whatsapp-float {
        left: 15px;
    }

    [dir="ltr"] .call-float {
        left: 15px;
    }
}

/* Enhanced Glow Effect */
.whatsapp-button::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: conic-gradient(from 0deg, #25d366, #128c7e, #25d366, #128c7e, #25d366);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: rotate 3s linear infinite;
}

.whatsapp-button:hover::before {
    opacity: 0.8;
}

.whatsapp-button::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    border-radius: 50%;
    z-index: -1;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Success Animation */
.whatsapp-button.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.whatsapp-button.success .whatsapp-icon::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
}

/* Loading Animation */
.whatsapp-button.loading .whatsapp-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* ===== WhatsApp Contact Card Styles ===== */
.card-success {
    border: 2px solid #25d366;
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.05) 0%, rgba(18, 140, 126, 0.05) 100%);
}

.icon-whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin: 0 auto 1rem;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.icon-whatsapp::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.icon-whatsapp:hover::before {
    animation: shine 0.6s ease;
}

.icon-whatsapp:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.4);
}

@keyframes shine {
    0% {
        opacity: 0;
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.btn-whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-whatsapp::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-whatsapp:hover::before {
    left: 100%;
}

.btn-whatsapp:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

.btn-whatsapp:active {
    transform: translateY(0);
}

.btn-whatsapp i {
    font-size: 18px;
    animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Enhanced Card Animation */
.card-success:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(37, 211, 102, 0.2);
    border-color: #25d366;
}

.card-success .pricing-header {
    position: relative;
}

.card-success .pricing-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #25d366, #128c7e, #25d366);
    background-size: 200% 100%;
    animation: gradient-move 3s ease infinite;
}

@keyframes gradient-move {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Attention Animation */
@keyframes attention {
    0% {
        transform: scale(1);
    }
    15% {
        transform: scale(1.1);
    }
    30% {
        transform: scale(1);
    }
    45% {
        transform: scale(1.1);
    }
    60% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Enhanced Mobile Experience */
@media (hover: none) and (pointer: coarse) {
    .whatsapp-button {
        animation: float 3s ease-in-out infinite, mobile-pulse 2s ease-in-out infinite;
    }

    @keyframes mobile-pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    .whatsapp-tooltip {
        display: none;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .whatsapp-button,
    .whatsapp-pulse,
    .whatsapp-pulse-2 {
        animation: none;
    }

    .whatsapp-button:hover {
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .whatsapp-button {
        border: 2px solid #000;
    }

    .whatsapp-tooltip {
        background: #000;
        border: 1px solid #fff;
    }
}

/* ===== Footer WhatsApp Styles ===== */
.whatsapp-item {
    position: relative;
}

.whatsapp-item i {
    color: #25d366 !important;
    font-size: 18px;
    animation: pulse-whatsapp 2s infinite;
}

@keyframes pulse-whatsapp {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.whatsapp-link {
    color: #25d366 !important;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
}

.whatsapp-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #25d366, #128c7e);
    transition: width 0.3s ease;
}

.whatsapp-link:hover {
    color: #128c7e !important;
    text-decoration: none;
    transform: translateX(3px);
}

.whatsapp-link:hover::before {
    width: 100%;
}

.whatsapp-item:hover i {
    animation: shake 0.5s ease;
    color: #128c7e !important;
}
