/* ===== تحسينات خاصة بالموبايل ===== */

/* تحسينات عامة للموبايل */
@media (max-width: 767px) {
    /* ضمان ترتيب العناصر في Hero Section */
    .hero-content {
        display: flex !important;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    /* لوحة التحكم تظهر أولاً */
    .hero-visual {
        order: -2 !important;
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }
    
    /* النص يظهر ثانياً */
    .hero-text {
        order: -1 !important;
        width: 100%;
    }
    
    /* تحسين عرض شارة "الأفضل في الشرق الأوسط" */
    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-sm);
        color: var(--white);
        margin-bottom: var(--spacing-md);
    }
    
    /* تحسين عرض العنوان */
    .hero-title {
        margin-bottom: var(--spacing-md);
        line-height: 1.3;
    }
    
    .hero-title .gradient-text {
        display: block;
        margin-top: var(--spacing-xs);
    }
    
    /* تحسين عرض الوصف */
    .hero-subtitle {
        margin-bottom: var(--spacing-lg);
        line-height: 1.6;
        max-width: 100%;
    }
    
    /* تحسين عرض الإحصائيات */
    .hero-stats {
        display: flex !important;
        justify-content: center;
        gap: var(--spacing-md);
        margin: var(--spacing-lg) 0 var(--spacing-xl);
        flex-wrap: wrap;
        width: 100%;
    }
    
    .hero-stats .stat-item {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        min-width: 80px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .hero-stats .stat-item:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.15);
    }
    
    /* تحسين عرض الأزرار */
    .hero-buttons {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
    
    .hero-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

/* تحسينات قسم تواصل معنا */
@media (max-width: 767px) {
    .contact .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .contact .section-badge {
        background: var(--primary);
        color: var(--white);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: 50px;
        font-size: var(--font-size-sm);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-md);
    }
    
    .contact .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
        color: var(--gray-900);
    }
    
    .contact .section-subtitle {
        font-size: var(--font-size-sm);
        color: var(--gray-600);
        line-height: 1.6;
    }
    
    /* تحسين بطاقات التواصل */
    .contact .pricing-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .contact .pricing-card {
        padding: var(--spacing-xl);
        text-align: center;
        border-radius: var(--radius-xl);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        background: var(--white);
    }
    
    .contact .pricing-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .contact .plan-icon {
        margin-bottom: var(--spacing-md);
    }
    
    .contact .plan-icon .icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        font-size: var(--font-size-xl);
        color: var(--white);
    }
    
    .contact .plan-name {
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
        color: var(--gray-900);
    }
    
    .contact .pricing-header p {
        font-size: var(--font-size-base);
        font-weight: 600;
        margin-bottom: var(--spacing-lg);
        color: var(--primary);
    }
    
    .contact .btn {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
        border-radius: var(--radius-lg);
        font-weight: 600;
        transition: all 0.3s ease;
    }
}

/* تحسينات الفوتر */
@media (max-width: 767px) {
    .footer {
        padding: var(--spacing-2xl) 0 var(--spacing-lg);
    }
    
    .footer-content {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .footer-section {
        padding: var(--spacing-lg) 0;
    }
    
    .footer-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .footer-logo .logo {
        height: 50px;
        width: auto;
        object-fit: contain;
    }
    
    .footer-logo h3 {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--white);
        margin: 0;
    }
    
    .footer-section p {
        font-size: var(--font-size-sm);
        line-height: 1.6;
        color: var(--gray-300);
        margin-bottom: var(--spacing-lg);
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .footer-section h4 {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--white);
        margin-bottom: var(--spacing-md);
    }
    
    .contact-info-footer {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }
    
    .contact-info-footer .contact-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
        color: var(--gray-300);
        padding: var(--spacing-xs) 0;
        min-height: 24px;
    }
    
    .contact-info-footer .contact-item i {
        color: var(--primary);
        width: 20px;
        text-align: center;
        flex-shrink: 0;
    }
    
    .contact-info-footer .whatsapp-link {
        color: var(--success) !important;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .contact-info-footer .whatsapp-link:hover {
        color: var(--success-dark) !important;
    }
    
    .footer-bottom {
        margin-top: var(--spacing-xl);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--gray-700);
        text-align: center;
    }
    
    .footer-bottom-content p {
        font-size: var(--font-size-xs);
        color: var(--gray-400);
        margin-bottom: var(--spacing-sm);
        line-height: 1.5;
    }
    
    .footer-bottom-content a {
        color: var(--primary);
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .footer-bottom-content a:hover {
        color: var(--primary-light);
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 399px) {
    .hero-badge {
        font-size: 11px;
        padding: 4px 12px;
    }
    
    .hero-title {
        font-size: var(--font-size-lg) !important;
    }
    
    .hero-subtitle {
        font-size: 13px !important;
    }
    
    .hero-stats {
        gap: var(--spacing-sm);
    }
    
    .hero-stats .stat-item {
        min-width: 70px;
        padding: var(--spacing-sm);
    }
    
    .hero-stats .stat-number {
        font-size: var(--font-size-lg) !important;
    }
    
    .hero-stats .stat-label {
        font-size: 10px !important;
    }
    
    .contact .pricing-card {
        padding: var(--spacing-lg);
    }
    
    .contact .plan-icon .icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .footer-logo h3 {
        font-size: var(--font-size-lg);
    }
    
    .contact-info-footer .contact-item {
        font-size: 12px;
    }
}
